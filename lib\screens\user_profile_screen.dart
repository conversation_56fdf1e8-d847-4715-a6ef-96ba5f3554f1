import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/l10n.dart';
import '../constants/constants.dart';
import '../utils/app_logger.dart';
import '../utils/toast_utils.dart';

class UserProfileScreen extends StatefulWidget {
  @override
  _UserProfileScreen createState() => _UserProfileScreen();
}

class _UserProfileScreen extends State<UserProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  String _phone = '';
  String _smsCode = '';
  bool _isLoading = false;
  bool _isGettingCode = false;
  int _countDown = 60;
  Timer? _timer;

  TextEditingController _phoneController = TextEditingController();
  TextEditingController _smsCodeController = TextEditingController();

  void _login() async {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();

    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<AuthProvider>(context, listen: false)
          .changePhone(_phone, _smsCode);
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(Routes.home);
      }
    } catch (e, stackTrace) {
      AppLogger.error('手机号更改失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 获取手机验证码
  void _getVerificationCode() async {
    if (_phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先输入新手机号')),
      );
      return;
    }

    if (!RegExp(r'^[0-9]{11}$').hasMatch(_phoneController.text)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请输入有效的新手机号')),
      );
      return;
    }

    setState(() {
      _isGettingCode = true;
      _countDown = 60;
    });

    try {
      await Provider.of<AuthProvider>(context, listen: false)
          .getSmsCode(_phoneController.text);
      _timer = Timer.periodic(Duration(seconds: 1), (timer) {
        setState(() {
          if (_countDown > 0) {
            _countDown--;
          } else {
            _isGettingCode = false;
            _timer?.cancel();
          }
        });
      });
    } catch (e, stackTrace) {
      AppLogger.error('获取验证码失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
      setState(() {
        _isGettingCode = false;
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    L10n.init(context); // 初始化本地化?

    return Scaffold(
      appBar: AppBar(
        // 2. 将AppBar的背景色设置为你想要的颜色
        backgroundColor: Colors.grey[60],
        elevation: 1,
        toolbarHeight: 0,
      ),
      backgroundColor: Colors.grey[60],
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, top: 50.0),
              child: Column(
                children: [
                  const SizedBox(height: 10),
                  // 登录表单
                  _buildLoginForm(),
                ],
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // 手机号输入框
          _buildPhoneField(),

          // 分隔线
          Divider(
            color: Colors.grey.shade300,
            height: 2,
            thickness: 0.5,
          ),

          // 验证码输入框
          _buildSmsCodeField(),

          // 分隔线
          Divider(
            color: Colors.grey.shade300,
            height: 2,
            thickness: 0.5,
          ),

          const SizedBox(height: 30),

          // 登录按钮
          SizedBox(
            width: double.infinity,
            height: 44,
            child: ElevatedButton(
              onPressed: _isLoading ? null : _login,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade400,
                foregroundColor: Colors.white,
                elevation: 2,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      '更改手机号',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[60],
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _phoneController,
        keyboardType: TextInputType.phone,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          hintText: '请输入新手机号码',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          prefixIcon: Icon(
            Icons.smartphone,
            color: Colors.black54,
            size: 23,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) return '请输入新手机号';
          if (!RegExp(r'^[0-9]{11}$').hasMatch(value)) return '请输入有效的新手机号';
          return null;
        },
        onSaved: (value) => _phone = value!.trim(),
      ),
    );
  }

  /// 构建验证码输入框
  Widget _buildSmsCodeField() {
    return Container(
      margin: EdgeInsets.only(top: 6),
      decoration: BoxDecoration(
        color: Colors.grey[60],
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _smsCodeController,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          hintText: '请输入验证码',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          prefixIcon: Icon(
            Icons.lock_outline,
            color: Colors.black54,
            size: 23,
          ),
          suffixIcon: Container(
            margin: const EdgeInsets.all(4),
            child: TextButton(
              onPressed: _isGettingCode ? null : _getVerificationCode,
              style: TextButton.styleFrom(
                backgroundColor: _isGettingCode
                    ? Colors.grey.shade200
                    : Colors.orange.shade50,
                foregroundColor: _isGettingCode
                    ? Colors.grey.shade500
                    : Colors.orange.shade600,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text(
                _isGettingCode ? '${_countDown}s后重新获取' : '获取验证码',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) return '请输入验证码';
          if (value.length < 6) return '验证码长度至少为6位';
          return null;
        },
        onSaved: (value) => _smsCode = value!.trim(),
      ),
    );
  }
}
