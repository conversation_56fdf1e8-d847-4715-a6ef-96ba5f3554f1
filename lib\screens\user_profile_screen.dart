import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/l10n.dart';
import '../constants/constants.dart';
import '../constants/user_level.dart';
import '../utils/app_logger.dart';
import '../utils/toast_utils.dart';

class UserProfileScreen extends StatefulWidget {
  @override
  _UserProfileScreen createState() => _UserProfileScreen();
}

class _UserProfileScreen extends State<UserProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  String _phone = '';
  String _smsCode = '';
  String _currentPhone = '';
  bool _isLoading = false;
  bool _isGettingCode = false;
  bool _showPhoneChangeForm = false;
  int _countDown = 60;
  Timer? _timer;

  TextEditingController _phoneController = TextEditingController();
  TextEditingController _smsCodeController = TextEditingController();
  TextEditingController _currentPhoneController = TextEditingController();

  void _updatePhone() async {
    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();

    // 验证当前手机号是否正确
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    if (authProvider.user?.phone != _currentPhone) {
      ToastUtils.showError('当前手机号不正确');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      await authProvider.changePhone(_phone, _smsCode);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('手机号更新成功'),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {
          _showPhoneChangeForm = false;
        });
        // 清空表单
        _phoneController.clear();
        _smsCodeController.clear();
        _currentPhoneController.clear();
        // 重置倒计时
        _timer?.cancel();
        _isGettingCode = false;
        _countDown = 60;
      }
    } catch (e, stackTrace) {
      AppLogger.error('手机号更改失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 获取手机验证码
  void _getVerificationCode() async {
    if (_phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先输入新手机号')),
      );
      return;
    }

    if (!RegExp(r'^[0-9]{11}$').hasMatch(_phoneController.text)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请输入有效的新手机号')),
      );
      return;
    }

    setState(() {
      _isGettingCode = true;
      _countDown = 60;
    });

    try {
      await Provider.of<AuthProvider>(context, listen: false)
          .getSmsCode(_phoneController.text);
      _timer = Timer.periodic(Duration(seconds: 1), (timer) {
        setState(() {
          if (_countDown > 0) {
            _countDown--;
          } else {
            _isGettingCode = false;
            _timer?.cancel();
          }
        });
      });
    } catch (e, stackTrace) {
      AppLogger.error('获取验证码失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
      setState(() {
        _isGettingCode = false;
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    _phoneController.dispose();
    _smsCodeController.dispose();
    _currentPhoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    L10n.init(context); // 初始化本地化?

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.grey[60],
        elevation: 1,
        toolbarHeight: 0,
      ),
      backgroundColor: Colors.grey[60],
      resizeToAvoidBottomInset: true,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, top: 50.0),
              child: Column(
                children: [
                  const SizedBox(height: 10),
                  // 用户信息展示卡片
                  _buildUserInfoCard(),
                  const SizedBox(height: 16),
                  // 用户等级描述卡片
                  _buildUserLevelCard(),
                  const SizedBox(height: 16),
                  // 账号管理卡片
                  _buildAccountManagementCard(),
                  const SizedBox(height: 16),
                  // 手机号更换表单（条件显示）
                  if (_showPhoneChangeForm) _buildPhoneChangeForm(),
                ],
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建用户信息展示卡片
  Widget _buildUserInfoCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;
        final userLevel = UserLevel.fromString(user?.memberShipLevel);

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // 左侧圆形人形图标
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.person,
                  size: 40,
                  color: Colors.black54,
                ),
              ),
              const SizedBox(width: 16),
              // 右侧用户信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户UID
                    Text(
                      'UID: ${user?.uid ?? '未知'}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // 浅灰色分隔线
                    Container(
                      height: 1,
                      color: Colors.grey[300],
                    ),
                    const SizedBox(height: 8),
                    // 用户等级
                    Text(
                      '用户等级：${userLevel.displayName}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建用户等级描述卡片
  Widget _buildUserLevelCard() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;
        final userLevel = UserLevel.fromString(user?.memberShipLevel);

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 等级描述文本
              Text(
                userLevel.getDescription(user?.memberShipExpireTime),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 16),
              // 升级/续费按钮
              Align(
                alignment: Alignment.centerRight,
                child: SizedBox(
                  width: 120,
                  height: 36,
                  child: ElevatedButton(
                    onPressed: () {
                      // TODO: 实现升级/续费功能
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('${userLevel.buttonText}功能待实现')),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black87,
                      foregroundColor: Colors.amber,
                      elevation: 2,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18),
                      ),
                    ),
                    child: Text(
                      userLevel.buttonText,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建账号管理卡片
  Widget _buildAccountManagementCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          setState(() {
            _showPhoneChangeForm = !_showPhoneChangeForm;
            // 如果展开表单，清空之前的输入
            if (_showPhoneChangeForm) {
              _phoneController.clear();
              _smsCodeController.clear();
              _currentPhoneController.clear();
              _timer?.cancel();
              _isGettingCode = false;
              _countDown = 60;
            }
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              // 更换手机号文本
              const Expanded(
                child: Text(
                  '更换手机号',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                ),
              ),
              // 警示图标和提示文本
              const Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 20,
              ),
              const SizedBox(width: 4),
              const Text(
                '请谨慎操作',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.orange,
                ),
              ),
              const SizedBox(width: 8),
              // 箭头图标
              Icon(
                _showPhoneChangeForm
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                color: Colors.grey,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建手机号更换表单
  Widget _buildPhoneChangeForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Form(
        key: _formKey,
        child: Column(
          children: [
            // 当前手机号输入框
            _buildCurrentPhoneField(),

            // 分隔线
            Divider(
              color: Colors.grey.shade300,
              height: 2,
              thickness: 0.5,
            ),

            // 新手机号输入框
            _buildPhoneField(),

            // 分隔线
            Divider(
              color: Colors.grey.shade300,
              height: 2,
              thickness: 0.5,
            ),

            // 验证码输入框
            _buildSmsCodeField(),

            // 分隔线
            Divider(
              color: Colors.grey.shade300,
              height: 2,
              thickness: 0.5,
            ),

            const SizedBox(height: 30),

            // 更新手机号按钮
            SizedBox(
              width: double.infinity,
              height: 44,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _updatePhone,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade400,
                  foregroundColor: Colors.white,
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(22),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        '更新手机号',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建当前手机号输入框
  Widget _buildCurrentPhoneField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[60],
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _currentPhoneController,
        keyboardType: TextInputType.phone,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          hintText: '请输入当前手机号码',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          prefixIcon: Icon(
            Icons.phone_android,
            color: Colors.black54,
            size: 23,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) return '请输入当前手机号';
          if (!RegExp(r'^[0-9]{11}$').hasMatch(value)) return '请输入有效的当前手机号';
          return null;
        },
        onSaved: (value) => _currentPhone = value!.trim(),
      ),
    );
  }

  /// 构建手机号输入框
  Widget _buildPhoneField() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[60],
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _phoneController,
        keyboardType: TextInputType.phone,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          hintText: '请输入新手机号码',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          prefixIcon: Icon(
            Icons.smartphone,
            color: Colors.black54,
            size: 23,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) return '请输入新手机号';
          if (!RegExp(r'^[0-9]{11}$').hasMatch(value)) return '请输入有效的新手机号';
          return null;
        },
        onSaved: (value) => _phone = value!.trim(),
      ),
    );
  }

  /// 构建验证码输入框
  Widget _buildSmsCodeField() {
    return Container(
      margin: EdgeInsets.only(top: 6),
      decoration: BoxDecoration(
        color: Colors.grey[60],
        borderRadius: BorderRadius.circular(8),
      ),
      child: TextFormField(
        controller: _smsCodeController,
        decoration: InputDecoration(
          border: InputBorder.none,
          enabledBorder: InputBorder.none,
          focusedBorder: InputBorder.none,
          errorBorder: InputBorder.none,
          focusedErrorBorder: InputBorder.none,
          disabledBorder: InputBorder.none,
          hintText: '请输入验证码',
          hintStyle: TextStyle(color: Colors.grey.shade500),
          prefixIcon: Icon(
            Icons.lock_outline,
            color: Colors.black54,
            size: 23,
          ),
          suffixIcon: Container(
            margin: const EdgeInsets.all(4),
            child: TextButton(
              onPressed: _isGettingCode ? null : _getVerificationCode,
              style: TextButton.styleFrom(
                backgroundColor: _isGettingCode
                    ? Colors.grey.shade200
                    : Colors.orange.shade50,
                foregroundColor: _isGettingCode
                    ? Colors.grey.shade500
                    : Colors.orange.shade600,
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              child: Text(
                _isGettingCode ? '${_countDown}s后重新获取' : '获取验证码',
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        ),
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
        validator: (value) {
          if (value == null || value.isEmpty) return '请输入验证码';
          if (value.length < 6) return '验证码长度至少为6位';
          return null;
        },
        onSaved: (value) => _smsCode = value!.trim(),
      ),
    );
  }
}
